<nav class="navbar">
  <div class="navbar-container">
    <a routerLink="/" class="navbar-logo">
      <img src="/assets/images/edarah-logo.svg" alt="Edarah Logo">
      <span>Edarah</span>
    </a>

    <div class="navbar-links">
      <a routerLink="/features" class="nav-link">{{ navTexts.features }}</a>
      <a routerLink="/pricing" class="nav-link">{{ navTexts.pricing }}</a>
      <a routerLink="/about" class="nav-link">{{ navTexts.about }}</a>
    </div>

    <div class="navbar-actions">
      <app-language-toggle></app-language-toggle>
      @if (currentUser) {
        <div class="user-menu">
          <button class="user-menu-button">
            {{ navTexts.welcome }}, {{ currentUser.first_name }}
            <svg class="chevron-down" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5z"></path></svg>
          </button>
          <div class="user-menu-dropdown">
            <a routerLink="/profile" class="dropdown-item">{{ navTexts.profile }}</a>
            <a (click)="logout()" class="dropdown-item">{{ navTexts.logout }}</a>
          </div>
        </div>
      } @else {
        <a routerLink="/login" class="btn btn-login">{{ navTexts.signIn }}</a>
        <a routerLink="/register" class="btn btn-register">{{ navTexts.getStarted }}</a>
      }
    </div>
  </div>
</nav>
