import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { Subscription } from 'rxjs';
import { LanguageService, Language } from '../../services/language.service';

interface PricingPlan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  isPopular?: boolean;
  buttonText: string;
  buttonClass: string;
}

interface Feature {
  icon: string;
  title: string;
  description: string;
}

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterLink],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit, OnDestroy {
  currentLanguage: Language = 'en';
  private languageSubscription!: Subscription;

  constructor(private languageService: LanguageService) {}

  ngOnInit() {
    this.languageSubscription = this.languageService.currentLanguage$.subscribe(language => {
      this.currentLanguage = language;
    });
  }

  ngOnDestroy() {
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe();
    }
  }
  
  features: Feature[] = [
    {
      icon: 'brain',
      title: 'Advanced AI Models',
      description: 'Access cutting-edge AI models for various tasks including text generation, image processing, and data analysis.'
    },
    {
      icon: 'zap',
      title: 'Lightning Fast',
      description: 'Get results in seconds with our optimized infrastructure and high-performance computing resources.'
    },
    {
      icon: 'shield',
      title: 'Secure & Private',
      description: 'Your data is protected with enterprise-grade security and privacy measures. We never store your sensitive information.'
    },
    {
      icon: 'code',
      title: 'Easy Integration',
      description: 'Simple APIs and SDKs make it easy to integrate our AI services into your existing applications and workflows.'
    },
    {
      icon: 'users',
      title: 'Team Collaboration',
      description: 'Work together with your team members, share projects, and manage access controls efficiently.'
    },
    {
      icon: 'chart',
      title: 'Analytics & Insights',
      description: 'Track usage, monitor performance, and gain insights into your AI-powered applications with detailed analytics.'
    }
  ];

  pricingPlans: PricingPlan[] = [
    {
      name: 'Starter',
      price: '$29',
      period: '/month',
      description: 'Perfect for individuals and small projects',
      features: [
        '10,000 API calls/month',
        'Basic AI models',
        'Email support',
        'Standard processing speed',
        'Basic analytics'
      ],
      buttonText: 'Get Started',
      buttonClass: 'btn-outline'
    },
    {
      name: 'Professional',
      price: '$99',
      period: '/month',
      description: 'Ideal for growing businesses and teams',
      features: [
        '100,000 API calls/month',
        'Advanced AI models',
        'Priority support',
        'Fast processing speed',
        'Advanced analytics',
        'Team collaboration',
        'Custom integrations'
      ],
      isPopular: true,
      buttonText: 'Start Free Trial',
      buttonClass: 'btn-primary'
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: '',
      description: 'For large organizations with specific needs',
      features: [
        'Unlimited API calls',
        'All AI models',
        'Dedicated support',
        'Fastest processing',
        'Custom analytics',
        'Advanced team features',
        'On-premise deployment',
        'SLA guarantee'
      ],
      buttonText: 'Contact Sales',
      buttonClass: 'btn-outline'
    }
  ];

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  onGetStarted() {
    // Navigate to registration or login
    console.log('Get started clicked');
  }

  onContactUs() {
    // Handle contact form or navigation
    console.log('Contact us clicked');
  }

  onSelectPlan(plan: PricingPlan) {
    console.log('Selected plan:', plan.name);
    // Handle plan selection
  }

  // Hero section translations
  get heroTexts() {
    return {
      title: this.currentLanguage === 'en'
        ? 'Unlock the Power of Artificial Intelligence'
        : 'اطلق قوة الذكاء الاصطناعي',
      description: this.currentLanguage === 'en'
        ? 'Transform your business with our cutting-edge AI services. From natural language processing to computer vision, we provide the tools you need to innovate and scale.'
        : 'حول عملك باستخدام خدمات الذكاء الاصطناعي المتطورة. من معالجة اللغة الطبيعية إلى الرؤية الحاسوبية، نوفر لك الأدوات التي تحتاجها للابتكار والنمو.',
      startTrial: this.currentLanguage === 'en' ? 'Start Free Trial' : 'ابدأ التجربة المجانية',
      learnMore: this.currentLanguage === 'en' ? 'Learn More' : 'اعرف المزيد',
      apiCalls: this.currentLanguage === 'en' ? 'API Calls' : 'استدعاءات API',
      developers: this.currentLanguage === 'en' ? 'Developers' : 'المطورين',
      uptime: this.currentLanguage === 'en' ? 'Uptime' : 'وقت التشغيل'
    };
  }

  // Features section translations
  get featuresTexts() {
    return {
      sectionTitle: this.currentLanguage === 'en' ? 'Powerful AI Features' : 'مميزات الذكاء الاصطناعي القوية',
      sectionSubtitle: this.currentLanguage === 'en'
        ? 'Discover the comprehensive suite of AI tools and services designed to accelerate your innovation'
        : 'اكتشف مجموعة شاملة من أدوات وخدمات الذكاء الاصطناعي المصممة لتسريع الابتكار لديك'
    };
  }

  // Translated features data
  get translatedFeatures() {
    if (this.currentLanguage === 'en') {
      return this.features;
    }

    return [
      {
        icon: 'brain',
        title: 'نماذج ذكاء اصطناعي متقدمة',
        description: 'الوصول إلى نماذج الذكاء الاصطناعي المتطورة لمختلف المهام بما في ذلك توليد النصوص ومعالجة الصور وتحليل البيانات.'
      },
      {
        icon: 'zap',
        title: 'سرعة البرق',
        description: 'احصل على النتائج في ثوانٍ مع البنية التحتية المحسنة وموارد الحوسبة عالية الأداء.'
      },
      {
        icon: 'shield',
        title: 'آمن وخاص',
        description: 'بياناتك محمية بأمان وخصوصية على مستوى المؤسسات. نحن لا نخزن معلوماتك الحساسة أبداً.'
      },
      {
        icon: 'code',
        title: 'تكامل سهل',
        description: 'واجهات برمجة التطبيقات البسيطة ومجموعات التطوير تجعل من السهل دمج خدمات الذكاء الاصطناعي في تطبيقاتك وسير العمل الحالي.'
      },
      {
        icon: 'users',
        title: 'تعاون الفريق',
        description: 'اعمل مع أعضاء فريقك، وشارك المشاريع، وأدر ضوابط الوصول بكفاءة.'
      },
      {
        icon: 'chart',
        title: 'التحليلات والرؤى',
        description: 'تتبع الاستخدام، ومراقبة الأداء، واكتساب رؤى حول تطبيقاتك المدعومة بالذكاء الاصطناعي مع التحليلات التفصيلية.'
      }
    ];
  }

  // Pricing section translations
  get pricingTexts() {
    return {
      sectionTitle: this.currentLanguage === 'en' ? 'Simple, Transparent Pricing' : 'أسعار بسيطة وشفافة',
      sectionSubtitle: this.currentLanguage === 'en'
        ? 'Select the perfect plan for your needs. Start free and scale as you grow.'
        : 'اختر الخطة المثالية لاحتياجاتك. ابدأ مجاناً وتوسع مع نموك.',
      popular: this.currentLanguage === 'en' ? 'Most Popular' : 'الأكثر شعبية',
      month: this.currentLanguage === 'en' ? '/month' : '/شهر'
    };
  }

  // Translated pricing plans
  get translatedPricingPlans() {
    if (this.currentLanguage === 'en') {
      return this.pricingPlans;
    }

    return [
      {
        name: 'المبتدئ',
        price: '$29',
        period: '/شهر',
        description: 'مثالي للأفراد والمشاريع الصغيرة',
        features: [
          '10,000 استدعاء API/شهر',
          'نماذج ذكاء اصطناعي أساسية',
          'دعم عبر البريد الإلكتروني',
          'سرعة معالجة قياسية',
          'تحليلات أساسية'
        ],
        buttonText: 'ابدأ الآن',
        buttonClass: 'btn-outline'
      },
      {
        name: 'المحترف',
        price: '$99',
        period: '/شهر',
        description: 'مثالي للشركات النامية والفرق',
        features: [
          '100,000 استدعاء API/شهر',
          'نماذج ذكاء اصطناعي متقدمة',
          'دعم ذو أولوية',
          'سرعة معالجة سريعة',
          'تحليلات متقدمة',
          'تعاون الفريق',
          'تكاملات مخصصة'
        ],
        isPopular: true,
        buttonText: 'ابدأ التجربة المجانية',
        buttonClass: 'btn-primary'
      },
      {
        name: 'المؤسسة',
        price: 'مخصص',
        period: '',
        description: 'للمؤسسات الكبيرة ذات الاحتياجات المحددة',
        features: [
          'استدعاءات API غير محدودة',
          'نماذج ذكاء اصطناعي مخصصة',
          'دعم مخصص 24/7',
          'أسرع سرعة معالجة',
          'تحليلات مؤسسية',
          'إدارة فريق متقدمة',
          'تكاملات مخصصة',
          'اتفاقية مستوى الخدمة',
          'تدريب مخصص'
        ],
        buttonText: 'اتصل بالمبيعات',
        buttonClass: 'btn-outline'
      }
    ];
  }

  // Contact section translations
  get contactTexts() {
    return {
      sectionTitle: this.currentLanguage === 'en' ? 'Get in Touch' : 'تواصل معنا',
      sectionDescription: this.currentLanguage === 'en'
        ? 'Ready to transform your business with AI? Contact our team to learn more about our services and find the perfect solution for your needs.'
        : 'مستعد لتحويل عملك بالذكاء الاصطناعي؟ اتصل بفريقنا لمعرفة المزيد عن خدماتنا والعثور على الحل المثالي لاحتياجاتك.',
      ctaTitle: this.currentLanguage === 'en' ? 'Ready to Get Started?' : 'مستعد للبدء؟',
      ctaSubtitle: this.currentLanguage === 'en'
        ? 'Join thousands of developers and businesses already using our AI services.'
        : 'انضم إلى آلاف المطورين والشركات التي تستخدم بالفعل خدمات الذكاء الاصطناعي لدينا.',
      emailUs: this.currentLanguage === 'en' ? 'Email Us' : 'راسلنا',
      callUs: this.currentLanguage === 'en' ? 'Call Us' : 'اتصل بنا',
      visitUs: this.currentLanguage === 'en' ? 'Visit Us' : 'زرنا'
    };
  }

  // Footer section translations
  get footerTexts() {
    return {
      product: this.currentLanguage === 'en' ? 'Product' : 'المنتج',
      features: this.currentLanguage === 'en' ? 'Features' : 'المميزات',
      pricing: this.currentLanguage === 'en' ? 'Pricing' : 'الأسعار',
      apiDocs: this.currentLanguage === 'en' ? 'API Documentation' : 'وثائق API',
      sdks: this.currentLanguage === 'en' ? 'SDKs' : 'مجموعات التطوير',
      company: this.currentLanguage === 'en' ? 'Company' : 'الشركة',
      aboutUs: this.currentLanguage === 'en' ? 'About Us' : 'من نحن',
      careers: this.currentLanguage === 'en' ? 'Careers' : 'الوظائف',
      blog: this.currentLanguage === 'en' ? 'Blog' : 'المدونة',
      contact: this.currentLanguage === 'en' ? 'Contact' : 'اتصل بنا',
      support: this.currentLanguage === 'en' ? 'Support' : 'الدعم',
      helpCenter: this.currentLanguage === 'en' ? 'Help Center' : 'مركز المساعدة',
      community: this.currentLanguage === 'en' ? 'Community' : 'المجتمع',
      status: this.currentLanguage === 'en' ? 'Status' : 'الحالة',
      security: this.currentLanguage === 'en' ? 'Security' : 'الأمان',
      copyright: this.currentLanguage === 'en'
        ? '© 2024 Edarah. All rights reserved.'
        : '© 2024 إدارة. جميع الحقوق محفوظة.',
      description: this.currentLanguage === 'en'
        ? 'Empowering businesses with cutting-edge AI solutions for the future of innovation.'
        : 'تمكين الشركات بحلول الذكاء الاصطناعي المتطورة لمستقبل الابتكار.'
    };
  }
}
