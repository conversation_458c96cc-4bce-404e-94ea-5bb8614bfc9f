import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { LanguageToggleComponent } from '../language-toggle/language-toggle.component';
import { LanguageService, Language } from '../../services/language.service';

@Component({
  selector: 'app-navigation',
  standalone: true,
  imports: [CommonModule, RouterLink, LanguageToggleComponent],
  templateUrl: './navigation.component.html',
  styleUrl: './navigation.component.css'
})
export class NavigationComponent implements OnInit, OnDestroy {
  currentUser: any | null = null;
  currentLanguage: Language = 'en';
  private userSubscription!: Subscription;
  private languageSubscription!: Subscription;

  constructor(
    private authService: AuthService,
    private router: Router,
    private languageService: LanguageService
  ) {}

  ngOnInit() {
    this.userSubscription = this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    this.languageSubscription = this.languageService.currentLanguage$.subscribe(language => {
      this.currentLanguage = language;
    });
  }

  ngOnDestroy() {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe();
    }
  }

  logout() {
    this.authService.logout();
  }

  // Navigation text translations
  get navTexts() {
    return {
      features: this.currentLanguage === 'en' ? 'Features' : 'المميزات',
      pricing: this.currentLanguage === 'en' ? 'Pricing' : 'الأسعار',
      about: this.currentLanguage === 'en' ? 'About' : 'حول',
      signIn: this.currentLanguage === 'en' ? 'Sign In' : 'تسجيل الدخول',
      getStarted: this.currentLanguage === 'en' ? 'Get Started' : 'ابدأ الآن',
      welcome: this.currentLanguage === 'en' ? 'Welcome' : 'مرحباً',
      profile: this.currentLanguage === 'en' ? 'Profile' : 'الملف الشخصي',
      logout: this.currentLanguage === 'en' ? 'Logout' : 'تسجيل الخروج'
    };
  }
}
