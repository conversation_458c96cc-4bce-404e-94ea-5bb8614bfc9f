<div class="login-container">
  <div class="login-card">
    <!-- Logo Section -->
    <div class="logo-section">
      <img src="/assets/images/edarah-logo.svg" alt="Edarah" class="logo">
      <h1>Welcome Back</h1>
      <p>Sign in to access your AI services</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <!-- Email Field -->
      <div class="form-group">
        <label for="email">Email Address</label>
        <div class="input-wrapper">
          <input
            type="email"
            id="email"
            formControlName="email"
            placeholder="Enter your email"
            [class.error]="email?.invalid && email?.touched"
          >
          <svg class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
            <polyline points="22,6 12,13 2,6"></polyline>
          </svg>
        </div>
        @if (email?.invalid && email?.touched) {
          <div class="error-message">
            {{ getFieldError('email') }}
          </div>
        }
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password">Password</label>
        <div class="input-wrapper">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            formControlName="password"
            placeholder="Enter your password"
            [class.error]="password?.invalid && password?.touched"
          >
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            aria-label="Toggle password visibility"
          >
            @if (!showPassword) {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
            } @else {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                <line x1="1" y1="1" x2="23" y2="23"></line>
              </svg>
            }
          </button>
        </div>
        @if (password?.invalid && password?.touched) {
          <div class="error-message">
            {{ getFieldError('password') }}
          </div>
        }
      </div>

      <!-- Remember Me & Forgot Password -->
      <div class="form-options">
        <label class="checkbox-wrapper">
          <input type="checkbox" formControlName="rememberMe">
          <span class="checkmark"></span>
          Remember me
        </label>
        <a href="#" class="forgot-password">Forgot password?</a>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="submit-btn"
        [disabled]="isSubmitting"
        [class.loading]="isSubmitting"
      >
        @if (!isSubmitting) {
          <span>Sign In</span>
        } @else {
          <span class="loading-text">
            <svg class="spinner" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
              </circle>
            </svg>
            Signing in...
          </span>
        }
      </button>

      <!-- Sign Up Link -->
      <div class="signup-link">
        Don't have an account? 
        <a routerLink="/register" class="link">Sign up here</a>
      </div>
    </form>
  </div>
</div>
