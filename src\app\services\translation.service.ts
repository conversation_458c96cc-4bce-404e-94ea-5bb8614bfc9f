import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface Translation {
  [key: string]: string | Translation;
}

export type Language = 'en' | 'ar';

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private currentLanguageSubject = new BehaviorSubject<Language>('en');
  private translationsSubject = new BehaviorSubject<Translation>({});

  public currentLanguage$ = this.currentLanguageSubject.asObservable();
  public translations$ = this.translationsSubject.asObservable();

  private translations: { [key in Language]: Translation } = {
    en: {},
    ar: {}
  };

  constructor() {
    this.loadTranslations();
    // Load saved language preference or default to English
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
      this.setLanguage(savedLanguage);
    }
  }

  private async loadTranslations() {
    try {
      // Load English translations
      const enResponse = await fetch('/assets/i18n/en.json');
      this.translations.en = await enResponse.json();

      // Load Arabic translations
      const arResponse = await fetch('/assets/i18n/ar.json');
      this.translations.ar = await arResponse.json();

      // Update current translations
      this.updateCurrentTranslations();
    } catch (error) {
      console.error('Error loading translations:', error);
      // Fallback to empty translations
      this.translations.en = {};
      this.translations.ar = {};
    }
  }

  private updateCurrentTranslations() {
    const currentLang = this.currentLanguageSubject.value;
    this.translationsSubject.next(this.translations[currentLang]);
  }

  setLanguage(language: Language) {
    this.currentLanguageSubject.next(language);
    this.updateCurrentTranslations();
    localStorage.setItem('language', language);
    
    // Update document direction and language
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
  }

  getCurrentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  toggleLanguage() {
    const currentLang = this.getCurrentLanguage();
    const newLang: Language = currentLang === 'en' ? 'ar' : 'en';
    this.setLanguage(newLang);
  }

  translate(key: string): string {
    const keys = key.split('.');
    let translation: any = this.translations[this.getCurrentLanguage()];
    
    for (const k of keys) {
      if (translation && typeof translation === 'object' && k in translation) {
        translation = translation[k];
      } else {
        // Fallback to English if key not found in current language
        translation = this.translations.en;
        for (const fallbackKey of keys) {
          if (translation && typeof translation === 'object' && fallbackKey in translation) {
            translation = translation[fallbackKey];
          } else {
            return key; // Return key if translation not found
          }
        }
        break;
      }
    }
    
    return typeof translation === 'string' ? translation : key;
  }

  // Helper method for reactive translations in templates
  getTranslation(key: string): Observable<string> {
    return new Observable(observer => {
      const updateTranslation = () => {
        observer.next(this.translate(key));
      };
      
      updateTranslation();
      
      const subscription = this.currentLanguage$.subscribe(() => {
        updateTranslation();
      });
      
      return () => subscription.unsubscribe();
    });
  }
}
