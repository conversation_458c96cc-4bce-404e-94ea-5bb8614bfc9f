.language-toggle-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.625rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 1rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  white-space: nowrap;
}

.language-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.language-toggle-btn:hover::before {
  opacity: 1;
}

.language-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.language-toggle-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.language-toggle-btn:focus {
  outline: none;
  ring: 3px;
  ring-color: rgba(102, 126, 234, 0.5);
  ring-offset: 2px;
}

.toggle-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.language-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.current-lang {
  font-size: 0.75rem;
  font-weight: 700;
  letter-spacing: 0.05em;
}

.separator {
  width: 1px;
  height: 0.75rem;
  background: rgba(255, 255, 255, 0.4);
}

.next-lang {
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.7;
  letter-spacing: 0.05em;
}

.toggle-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

.button-text {
  position: relative;
  z-index: 1;
  font-weight: 600;
  letter-spacing: 0.025em;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .button-text {
    display: none;
  }

  .language-toggle-btn {
    padding: 0.625rem;
    gap: 0;
  }

  .toggle-content {
    gap: 0.375rem;
  }
}

/* RTL support */
:host-context([dir="rtl"]) .language-toggle-btn {
  flex-direction: row-reverse;
}

:host-context([dir="rtl"]) .toggle-content {
  flex-direction: row-reverse;
}

:host-context(.arabic) .language-toggle-btn {
  flex-direction: row-reverse;
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

:host-context(.arabic) .toggle-content {
  flex-direction: row-reverse;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .language-toggle-btn {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  }

  .language-toggle-btn::before {
    background: linear-gradient(135deg, #7c3aed 0%, #4f46e5 100%);
  }
}
