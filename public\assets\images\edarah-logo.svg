<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4299e1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="95" fill="url(#logoGradient)" stroke="none"/>
  
  <!-- Letter E -->
  <path d="M50 60 L50 140 L120 140 L120 125 L65 125 L65 105 L110 105 L110 90 L65 90 L65 75 L120 75 L120 60 Z" 
        fill="white" stroke="none"/>
  
  <!-- Letter D -->
  <path d="M130 60 L130 140 L160 140 Q180 140 180 120 L180 80 Q180 60 160 60 Z M145 75 L160 75 Q165 75 165 80 L165 120 Q165 125 160 125 L145 125 Z" 
        fill="white" stroke="none"/>
  
  <!-- Upward Arrow -->
  <path d="M100 30 L85 45 L95 45 L95 55 L105 55 L105 45 L115 45 Z" 
        fill="url(#arrowGradient)" stroke="none"/>
  
  <!-- Small connecting elements -->
  <circle cx="75" cy="50" r="3" fill="white" opacity="0.8"/>
  <circle cx="125" cy="50" r="3" fill="white" opacity="0.8"/>
  <circle cx="100" cy="170" r="4" fill="white" opacity="0.6"/>
</svg>
