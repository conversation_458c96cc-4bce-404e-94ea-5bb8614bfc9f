/* Edarah Global Styles */

/* CSS Variables for Edarah Brand Colors */
:root {
  /* Primary Brand Colors */
  --edarah-primary: #1E88E5;
  --edarah-primary-dark: #0D47A1;
  --edarah-primary-light: #42A5F5;
  --edarah-accent: #0A2E5C;

  /* Gradient Colors */
  --edarah-gradient: linear-gradient(135deg, #42A5F5 0%, #1E88E5 50%, #0D47A1 100%);
  --edarah-gradient-light: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 50%, #90CAF9 100%);

  /* Neutral Colors */
  --edarah-white: #FFFFFF;
  --edarah-light-gray: #F5F7FA;
  --edarah-gray: #E0E6ED;
  --edarah-dark-gray: #8B9CB5;
  --edarah-text-dark: #2D3748;
  --edarah-text-light: #718096;

  /* Status Colors */
  --edarah-success: #48BB78;
  --edarah-warning: #ED8936;
  --edarah-error: #F56565;

  /* Shadows */
  --edarah-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --edarah-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --edarah-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --edarah-shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --edarah-radius-sm: 4px;
  --edarah-radius-md: 8px;
  --edarah-radius-lg: 12px;
  --edarah-radius-xl: 16px;

  /* Spacing */
  --edarah-space-xs: 4px;
  --edarah-space-sm: 8px;
  --edarah-space-md: 16px;
  --edarah-space-lg: 24px;
  --edarah-space-xl: 32px;
  --edarah-space-2xl: 48px;
  --edarah-space-3xl: 64px;

  /* Typography */
  --edarah-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --edarah-font-size-xs: 12px;
  --edarah-font-size-sm: 14px;
  --edarah-font-size-md: 16px;
  --edarah-font-size-lg: 18px;
  --edarah-font-size-xl: 20px;
  --edarah-font-size-2xl: 24px;
  --edarah-font-size-3xl: 30px;
  --edarah-font-size-4xl: 36px;
  --edarah-font-size-5xl: 48px;
}

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--edarah-font-family);
  font-size: var(--edarah-font-size-md);
  line-height: 1.6;
  color: var(--edarah-text-dark);
  background-color: var(--edarah-white);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Classes */
.edarah-heading-1 {
  font-size: var(--edarah-font-size-5xl);
  font-weight: 700;
  line-height: 1.2;
  color: var(--edarah-text-dark);
}

.edarah-heading-2 {
  font-size: var(--edarah-font-size-4xl);
  font-weight: 600;
  line-height: 1.3;
  color: var(--edarah-text-dark);
}

.edarah-heading-3 {
  font-size: var(--edarah-font-size-3xl);
  font-weight: 600;
  line-height: 1.3;
  color: var(--edarah-text-dark);
}

.edarah-heading-4 {
  font-size: var(--edarah-font-size-2xl);
  font-weight: 600;
  line-height: 1.4;
  color: var(--edarah-text-dark);
}

.edarah-text-large {
  font-size: var(--edarah-font-size-lg);
  line-height: 1.6;
  color: var(--edarah-text-dark);
}

.edarah-text-body {
  font-size: var(--edarah-font-size-md);
  line-height: 1.6;
  color: var(--edarah-text-light);
}

.edarah-text-small {
  font-size: var(--edarah-font-size-sm);
  line-height: 1.5;
  color: var(--edarah-text-light);
}

/* Button Styles */
.edarah-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--edarah-space-md) var(--edarah-space-lg);
  border: none;
  border-radius: var(--edarah-radius-md);
  font-family: var(--edarah-font-family);
  font-size: var(--edarah-font-size-md);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 44px;
}

.edarah-btn-primary {
  background: var(--edarah-gradient);
  color: var(--edarah-white);
  box-shadow: var(--edarah-shadow-md);
}

.edarah-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--edarah-shadow-lg);
}

.edarah-btn-secondary {
  background: var(--edarah-white);
  color: var(--edarah-primary);
  border: 2px solid var(--edarah-primary);
}

.edarah-btn-secondary:hover {
  background: var(--edarah-primary);
  color: var(--edarah-white);
}

.edarah-btn-outline {
  background: transparent;
  color: var(--edarah-primary);
  border: 1px solid var(--edarah-gray);
}

.edarah-btn-outline:hover {
  background: var(--edarah-light-gray);
  border-color: var(--edarah-primary);
}

/* Form Styles */
.edarah-form-group {
  margin-bottom: var(--edarah-space-lg);
}

.edarah-label {
  display: block;
  margin-bottom: var(--edarah-space-sm);
  font-size: var(--edarah-font-size-sm);
  font-weight: 500;
  color: var(--edarah-text-dark);
}

.edarah-input {
  width: 100%;
  padding: var(--edarah-space-md);
  border: 1px solid var(--edarah-gray);
  border-radius: var(--edarah-radius-md);
  font-family: var(--edarah-font-family);
  font-size: var(--edarah-font-size-md);
  color: var(--edarah-text-dark);
  background-color: var(--edarah-white);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.edarah-input:focus {
  outline: none;
  border-color: var(--edarah-primary);
  box-shadow: 0 0 0 3px rgba(30, 136, 229, 0.1);
}

.edarah-input::placeholder {
  color: var(--edarah-dark-gray);
}

/* Card Styles */
.edarah-card {
  background: var(--edarah-white);
  border-radius: var(--edarah-radius-lg);
  box-shadow: var(--edarah-shadow-md);
  padding: var(--edarah-space-xl);
  border: 1px solid var(--edarah-gray);
}

.edarah-card-header {
  margin-bottom: var(--edarah-space-lg);
  text-align: center;
}

.edarah-card-body {
  margin-bottom: var(--edarah-space-lg);
}

.edarah-card-footer {
  text-align: center;
  padding-top: var(--edarah-space-lg);
  border-top: 1px solid var(--edarah-gray);
}

/* Layout Utilities */
.edarah-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--edarah-space-lg);
}

.edarah-container-sm {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--edarah-space-lg);
}

.edarah-flex {
  display: flex;
}

.edarah-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.edarah-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edarah-grid {
  display: grid;
}

.edarah-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--edarah-space-lg);
}

.edarah-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--edarah-space-lg);
}

/* Spacing Utilities */
.edarah-mt-xs { margin-top: var(--edarah-space-xs); }
.edarah-mt-sm { margin-top: var(--edarah-space-sm); }
.edarah-mt-md { margin-top: var(--edarah-space-md); }
.edarah-mt-lg { margin-top: var(--edarah-space-lg); }
.edarah-mt-xl { margin-top: var(--edarah-space-xl); }
.edarah-mt-2xl { margin-top: var(--edarah-space-2xl); }
.edarah-mt-3xl { margin-top: var(--edarah-space-3xl); }

.edarah-mb-xs { margin-bottom: var(--edarah-space-xs); }
.edarah-mb-sm { margin-bottom: var(--edarah-space-sm); }
.edarah-mb-md { margin-bottom: var(--edarah-space-md); }
.edarah-mb-lg { margin-bottom: var(--edarah-space-lg); }
.edarah-mb-xl { margin-bottom: var(--edarah-space-xl); }
.edarah-mb-2xl { margin-bottom: var(--edarah-space-2xl); }
.edarah-mb-3xl { margin-bottom: var(--edarah-space-3xl); }

/* Text Utilities */
.edarah-text-center { text-align: center; }
.edarah-text-left { text-align: left; }
.edarah-text-right { text-align: right; }

.edarah-text-primary { color: var(--edarah-primary); }
.edarah-text-white { color: var(--edarah-white); }
.edarah-text-gray { color: var(--edarah-text-light); }

/* Background Utilities */
.edarah-bg-primary { background-color: var(--edarah-primary); }
.edarah-bg-gradient { background: var(--edarah-gradient); }
.edarah-bg-light { background-color: var(--edarah-light-gray); }
.edarah-bg-white { background-color: var(--edarah-white); }

/* Responsive Design */
@media (max-width: 768px) {
  .edarah-container {
    padding: 0 var(--edarah-space-md);
  }

  .edarah-grid-2,
  .edarah-grid-3 {
    grid-template-columns: 1fr;
  }

  .edarah-heading-1 {
    font-size: var(--edarah-font-size-4xl);
  }

  .edarah-heading-2 {
    font-size: var(--edarah-font-size-3xl);
  }

  .edarah-card {
    padding: var(--edarah-space-lg);
  }
}
