.language-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: 2px solid var(--edarah-primary, #3b82f6);
  border-radius: 0.5rem;
  color: var(--edarah-primary, #3b82f6);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.language-toggle-btn:hover {
  background: var(--edarah-primary, #3b82f6);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.language-toggle-btn:active {
  transform: translateY(0);
}

.language-text {
  font-family: inherit;
  font-size: inherit;
}

.language-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* RTL support */
:host-context([dir="rtl"]) .language-toggle-btn {
  flex-direction: row-reverse;
}

:host-context(.arabic) .language-toggle-btn {
  flex-direction: row-reverse;
  font-family: 'Segoe UI', '<PERSON>homa', 'Arial', sans-serif;
}

/* Responsive design */
@media (max-width: 768px) {
  .language-toggle-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .language-icon {
    width: 1rem;
    height: 1rem;
  }
}

@media (max-width: 480px) {
  .language-text {
    display: none;
  }
  
  .language-toggle-btn {
    padding: 0.5rem;
    min-width: 2.5rem;
    justify-content: center;
  }
}
