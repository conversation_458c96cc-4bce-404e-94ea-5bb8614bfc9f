import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { LanguageService } from '../../services/language.service';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './register.component.html',
  styleUrl: './register.component.css'
})
export class RegisterComponent implements OnInit, OnDestroy {
  registerForm: FormGroup;
  isSubmitting = false;
  showPassword = false;
  showConfirmPassword = false;
  currentLanguage: string = 'en';
  private languageSubscription: Subscription = new Subscription();

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private languageService: LanguageService
  ) {
    this.registerForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: ['', [Validators.required, Validators.pattern('^[0-9]{10,15}$')]],
      password: ['', [Validators.required, Validators.minLength(8), this.passwordValidator]],
      confirmPassword: ['', [Validators.required]],
      agreeToTerms: [false, [Validators.requiredTrue]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit() {
    this.languageSubscription = this.languageService.currentLanguage$.subscribe(
      language => {
        this.currentLanguage = language;
      }
    );
  }

  ngOnDestroy() {
    this.languageSubscription.unsubscribe();
  }

  // Custom password validator
  passwordValidator(control: AbstractControl): {[key: string]: any} | null {
    const value = control.value;
    if (!value) return null;

    const hasNumber = /[0-9]/.test(value);
    const hasUpper = /[A-Z]/.test(value);
    const hasLower = /[a-z]/.test(value);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);

    const valid = hasNumber && hasUpper && hasLower && hasSpecial;
    if (!valid) {
      return { 'passwordStrength': true };
    }
    return null;
  }

  // Custom validator to check if passwords match
  passwordMatchValidator(form: AbstractControl): {[key: string]: any} | null {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ 'passwordMismatch': true });
      return { 'passwordMismatch': true };
    }
    
    if (confirmPassword?.errors?.['passwordMismatch']) {
      delete confirmPassword.errors['passwordMismatch'];
      if (Object.keys(confirmPassword.errors).length === 0) {
        confirmPassword.setErrors(null);
      }
    }
    
    return null;
  }

  get firstName() { return this.registerForm.get('firstName'); }
  get lastName() { return this.registerForm.get('lastName'); }
  get email() { return this.registerForm.get('email'); }
  get phoneNumber() { return this.registerForm.get('phoneNumber'); }
  get password() { return this.registerForm.get('password'); }
  get confirmPassword() { return this.registerForm.get('confirmPassword'); }
  get agreeToTerms() { return this.registerForm.get('agreeToTerms'); }

  // Translation getters
  get pageTexts() {
    return {
      title: this.currentLanguage === 'en' ? 'Create Account' : 'إنشاء حساب',
      subtitle: this.currentLanguage === 'en' ? 'Join Edarah to access powerful AI services' : 'انضم إلى إدارة للوصول إلى خدمات الذكاء الاصطناعي القوية',
      firstNameLabel: this.currentLanguage === 'en' ? 'First Name' : 'الاسم الأول',
      firstNamePlaceholder: this.currentLanguage === 'en' ? 'Enter first name' : 'أدخل الاسم الأول',
      lastNameLabel: this.currentLanguage === 'en' ? 'Last Name' : 'اسم العائلة',
      lastNamePlaceholder: this.currentLanguage === 'en' ? 'Enter last name' : 'أدخل اسم العائلة',
      emailLabel: this.currentLanguage === 'en' ? 'Email Address' : 'عنوان البريد الإلكتروني',
      emailPlaceholder: this.currentLanguage === 'en' ? 'Enter your email' : 'أدخل بريدك الإلكتروني',
      phoneLabel: this.currentLanguage === 'en' ? 'Phone Number' : 'رقم الهاتف',
      phonePlaceholder: this.currentLanguage === 'en' ? 'Enter phone number' : 'أدخل رقم الهاتف',
      passwordLabel: this.currentLanguage === 'en' ? 'Password' : 'كلمة المرور',
      passwordPlaceholder: this.currentLanguage === 'en' ? 'Create a strong password' : 'أنشئ كلمة مرور قوية',
      confirmPasswordLabel: this.currentLanguage === 'en' ? 'Confirm Password' : 'تأكيد كلمة المرور',
      confirmPasswordPlaceholder: this.currentLanguage === 'en' ? 'Confirm your password' : 'أكد كلمة المرور',
      agreeToTerms: this.currentLanguage === 'en' ? 'I agree to the' : 'أوافق على',
      termsOfService: this.currentLanguage === 'en' ? 'Terms of Service' : 'شروط الخدمة',
      and: this.currentLanguage === 'en' ? 'and' : 'و',
      privacyPolicy: this.currentLanguage === 'en' ? 'Privacy Policy' : 'سياسة الخصوصية',
      createAccountButton: this.currentLanguage === 'en' ? 'Create Account' : 'إنشاء حساب',
      creatingAccount: this.currentLanguage === 'en' ? 'Creating account...' : 'جاري إنشاء الحساب...',
      haveAccount: this.currentLanguage === 'en' ? 'Already have an account?' : 'لديك حساب بالفعل؟',
      signInLink: this.currentLanguage === 'en' ? 'Sign in here' : 'سجل الدخول هنا'
    };
  }

  get errorTexts() {
    return {
      firstNameRequired: this.currentLanguage === 'en' ? 'First name is required' : 'الاسم الأول مطلوب',
      firstNameMinLength: this.currentLanguage === 'en' ? 'First name must be at least 2 characters' : 'الاسم الأول يجب أن يكون حرفين على الأقل',
      lastNameRequired: this.currentLanguage === 'en' ? 'Last name is required' : 'اسم العائلة مطلوب',
      lastNameMinLength: this.currentLanguage === 'en' ? 'Last name must be at least 2 characters' : 'اسم العائلة يجب أن يكون حرفين على الأقل',
      emailRequired: this.currentLanguage === 'en' ? 'Email is required' : 'البريد الإلكتروني مطلوب',
      emailInvalid: this.currentLanguage === 'en' ? 'Please enter a valid email address' : 'يرجى إدخال عنوان بريد إلكتروني صحيح',
      phoneRequired: this.currentLanguage === 'en' ? 'Phone number is required' : 'رقم الهاتف مطلوب',
      phoneInvalid: this.currentLanguage === 'en' ? 'Please enter a valid phone number' : 'يرجى إدخال رقم هاتف صحيح',
      passwordRequired: this.currentLanguage === 'en' ? 'Password is required' : 'كلمة المرور مطلوبة',
      passwordMinLength: this.currentLanguage === 'en' ? 'Password must be at least 8 characters' : 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
      passwordStrength: this.currentLanguage === 'en'
        ? 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
        : 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل، وحرف صغير، ورقم، ورمز خاص',
      confirmPasswordRequired: this.currentLanguage === 'en' ? 'Confirm password is required' : 'تأكيد كلمة المرور مطلوب',
      passwordMismatch: this.currentLanguage === 'en' ? 'Passwords do not match' : 'كلمات المرور غير متطابقة',
      agreeToTermsRequired: this.currentLanguage === 'en' ? 'You must agree to the terms and conditions' : 'يجب الموافقة على الشروط والأحكام'
    };
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPasswordVisibility() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  onSubmit() {
    if (this.registerForm.valid) {
      this.isSubmitting = true;
      const { confirmPassword, agreeToTerms, ...userData } = this.registerForm.value;
      
      this.authService.register(userData).subscribe({
        next: (response) => {
          console.log('Registration successful', response);
          this.isSubmitting = false;
          // Optionally store token and navigate
          // localStorage.setItem('token', response.token);
          this.router.navigate(['/login']);
        },
        error: (err) => {
          console.error('Registration failed', err);
          this.isSubmitting = false;
          // You can display an error message to the user here
        }
      });
    } else {
      this.registerForm.markAllAsTouched();
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.registerForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        switch (fieldName) {
          case 'firstName': return this.errorTexts.firstNameRequired;
          case 'lastName': return this.errorTexts.lastNameRequired;
          case 'email': return this.errorTexts.emailRequired;
          case 'phoneNumber': return this.errorTexts.phoneRequired;
          case 'password': return this.errorTexts.passwordRequired;
          case 'confirmPassword': return this.errorTexts.confirmPasswordRequired;
          default: return this.errorTexts.firstNameRequired;
        }
      }
      if (field.errors['requiredTrue']) {
        return this.errorTexts.agreeToTermsRequired;
      }
      if (field.errors['email']) {
        return this.errorTexts.emailInvalid;
      }
      if (field.errors['minlength']) {
        switch (fieldName) {
          case 'firstName': return this.errorTexts.firstNameMinLength;
          case 'lastName': return this.errorTexts.lastNameMinLength;
          case 'password': return this.errorTexts.passwordMinLength;
          default: return this.errorTexts.passwordMinLength;
        }
      }
      if (field.errors['pattern']) {
        return this.errorTexts.phoneInvalid;
      }
      if (field.errors['passwordStrength']) {
        return this.errorTexts.passwordStrength;
      }
      if (field.errors['passwordMismatch']) {
        return this.errorTexts.passwordMismatch;
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: {[key: string]: string} = {
      'firstName': 'First name',
      'lastName': 'Last name',
      'email': 'Email',
      'phoneNumber': 'Phone number',
      'password': 'Password',
      'confirmPassword': 'Confirm password'
    };
    return displayNames[fieldName] || fieldName;
  }

  getPasswordStrength(): string {
    const password = this.password?.value || '';
    if (password.length === 0) return '';
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;

    if (strength <= 2) return 'weak';
    if (strength <= 3) return 'medium';
    return 'strong';
  }
}
