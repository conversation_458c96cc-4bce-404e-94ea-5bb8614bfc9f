/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 16px 0;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.brand-text {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
}

.nav-menu {
  display: flex;
  gap: 32px;
}

.nav-link {
  color: #4a5568;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #667eea;
}

.nav-actions {
  display: flex;
  gap: 16px;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding-top: 80px;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero-title {
  font-size: 56px;
  font-weight: 700;
  line-height: 1.1;
  color: #1a202c;
  margin-bottom: 24px;
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 20px;
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 40px;
}

.hero-actions {
  display: flex;
  gap: 20px;
  margin-bottom: 60px;
}

.hero-stats {
  display: flex;
  gap: 40px;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* AI Visualization */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-visualization {
  position: relative;
  width: 400px;
  height: 400px;
}

.ai-node {
  position: absolute;
  width: 60px;
  height: 60px;
  animation: float 3s ease-in-out infinite;
}

.ai-node:nth-child(1) { top: 20%; left: 20%; }
.ai-node:nth-child(2) { top: 20%; right: 20%; }
.ai-node:nth-child(3) { top: 50%; left: 10%; }
.ai-node:nth-child(4) { top: 50%; right: 10%; }
.ai-node:nth-child(5) { bottom: 30%; left: 25%; }
.ai-node:nth-child(6) { bottom: 30%; right: 25%; }
.ai-node:nth-child(7) { top: 35%; left: 45%; }
.ai-node:nth-child(8) { bottom: 45%; left: 45%; }

.node-core {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.node-ring {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  animation: ring 3s linear infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes ring {
  0% { transform: scale(1) rotate(0deg); opacity: 1; }
  100% { transform: scale(1.5) rotate(360deg); opacity: 0; }
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 48px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 16px;
}

.section-description {
  font-size: 20px;
  color: #4a5568;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Features Section */
.features {
  padding: 120px 0;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.feature-card {
  padding: 40px;
  border-radius: 16px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.feature-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.feature-icon svg {
  width: 30px;
  height: 30px;
  color: white;
  stroke-width: 2;
}

.feature-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.feature-description {
  color: #4a5568;
  line-height: 1.6;
}

/* Pricing Section */
.pricing {
  padding: 120px 0;
  background: #f7fafc;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.pricing-card {
  background: white;
  border-radius: 16px;
  padding: 40px;
  border: 2px solid #e2e8f0;
  position: relative;
  transition: all 0.3s ease;
}

.pricing-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.pricing-card.popular {
  border-color: #667eea;
  transform: scale(1.05);
}

.pricing-card.popular:hover {
  transform: scale(1.05) translateY(-8px);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.plan-header {
  text-align: center;
  margin-bottom: 40px;
}

.plan-name {
  font-size: 24px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.plan-price {
  margin-bottom: 16px;
}

.price {
  font-size: 48px;
  font-weight: 700;
  color: #1a202c;
}

.period {
  font-size: 18px;
  color: #718096;
}

.plan-description {
  color: #4a5568;
  line-height: 1.6;
}

.plan-features {
  margin-bottom: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.check-icon {
  width: 20px;
  height: 20px;
  color: #38a169;
  stroke-width: 2;
  flex-shrink: 0;
}

.plan-button {
  width: 100%;
  justify-content: center;
}

/* Contact Section */
.contact {
  padding: 120px 0;
  background: white;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.contact-methods {
  margin-top: 40px;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
}

.contact-icon {
  width: 24px;
  height: 24px;
  color: #667eea;
  stroke-width: 2;
}

.contact-method h4 {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.contact-method p {
  color: #4a5568;
}

.cta-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 40px;
  border-radius: 16px;
  text-align: center;
  color: white;
}

.cta-card h3 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 16px;
}

.cta-card p {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.cta-card .btn {
  background: white;
  color: #667eea;
}

.cta-card .btn:hover {
  background: #f7fafc;
}

/* Footer */
.footer {
  background: #1a202c;
  color: white;
  padding: 80px 0 40px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 80px;
  margin-bottom: 60px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-logo {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

.footer-description {
  color: #a0aec0;
  line-height: 1.6;
  max-width: 300px;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.link-group h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: white;
}

.link-group a {
  display: block;
  color: #a0aec0;
  text-decoration: none;
  margin-bottom: 12px;
  transition: color 0.3s ease;
}

.link-group a:hover {
  color: #667eea;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 40px;
  border-top: 1px solid #2d3748;
}

.footer-legal {
  display: flex;
  gap: 24px;
}

.footer-legal a {
  color: #a0aec0;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-legal a:hover {
  color: #667eea;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  .hero-title {
    font-size: 48px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }
}

@media (max-width: 1024px) {
  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .pricing-card.popular:hover {
    transform: translateY(-8px);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-actions {
    gap: 12px;
  }

  .nav-actions .btn {
    padding: 8px 16px;
    font-size: 12px;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-description {
    font-size: 18px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .hero-stats {
    justify-content: center;
    gap: 30px;
  }

  .section-title {
    font-size: 36px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .pricing-card.popular {
    transform: none;
  }

  .pricing-card.popular:hover {
    transform: translateY(-8px);
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .hero {
    padding-top: 100px;
  }

  .hero-title {
    font-size: 28px;
  }

  .hero-description {
    font-size: 16px;
  }

  .section-title {
    font-size: 28px;
  }

  .section-description {
    font-size: 16px;
  }

  .ai-visualization {
    width: 300px;
    height: 300px;
  }

  .ai-node {
    width: 40px;
    height: 40px;
  }
}
