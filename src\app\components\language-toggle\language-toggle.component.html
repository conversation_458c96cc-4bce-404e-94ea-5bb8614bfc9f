<button
  class="language-toggle-btn"
  (click)="toggleLanguage()"
  [title]="buttonTitle"
  type="button">
  <div class="toggle-content">
    <div class="language-indicator">
      <span class="current-lang">{{ currentLanguage.toUpperCase() }}</span>
      <div class="separator"></div>
      <span class="next-lang">{{ nextLanguage.toUpperCase() }}</span>
    </div>
    <svg class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
      <path d="M8 9l4-4 4 4"/>
      <path d="M16 15l-4 4-4-4"/>
    </svg>
  </div>
  <span class="button-text">{{ buttonText }}</span>
</button>
