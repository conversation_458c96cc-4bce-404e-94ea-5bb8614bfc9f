<!-- Hero Section -->
<section id="home" class="hero">
  <div class="hero-container">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span class="gradient-text">{{ heroTexts.title }}</span>
        </h1>
        <p class="hero-description">
          {{ heroTexts.description }}
        </p>
        <div class="hero-actions">
          <button class="btn btn-primary btn-large" (click)="onGetStarted()">
            {{ heroTexts.startTrial }}
          </button>
          <button class="btn btn-outline btn-large" (click)="scrollToSection('features')">
            {{ heroTexts.learnMore }}
          </button>
        </div>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number">10M+</span>
            <span class="stat-label">{{ heroTexts.apiCalls }}</span>
          </div>
          <div class="stat">
            <span class="stat-number">50K+</span>
            <span class="stat-label">{{ heroTexts.developers }}</span>
          </div>
          <div class="stat">
            <span class="stat-number">99.9%</span>
            <span class="stat-label">{{ heroTexts.uptime }}</span>
          </div>
        </div>
      </div>
      <div class="hero-visual">
        <div class="ai-visualization">
          @for (i of [1,2,3,4,5,6,7,8]; track i) {
            <div class="ai-node" [style.animation-delay.s]="i * 0.2">
              <div class="node-core"></div>
              <div class="node-ring"></div>
            </div>
          }
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section id="features" class="features">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{{ featuresTexts.sectionTitle }}</h2>
      <p class="section-description">
        {{ featuresTexts.sectionSubtitle }}
      </p>
    </div>

    <div class="features-grid">
      @for (feature of features; track feature.title) {
        <div class="feature-card">
          <div class="feature-icon">
            @if (feature.icon === 'brain') {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z"/>
                <path d="M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z"/>
              </svg>
            } @else if (feature.icon === 'zap') {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>
              </svg>
            } @else if (feature.icon === 'shield') {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
              </svg>
            } @else if (feature.icon === 'code') {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="16,18 22,12 16,6"/>
                <polyline points="8,6 2,12 8,18"/>
              </svg>
            } @else if (feature.icon === 'users') {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            } @else if (feature.icon === 'chart') {
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="20" x2="18" y2="10"/>
                <line x1="12" y1="20" x2="12" y2="4"/>
                <line x1="6" y1="20" x2="6" y2="14"/>
              </svg>
            }
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      }
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="pricing">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{{ pricingTexts.sectionTitle }}</h2>
      <p class="section-description">
        {{ pricingTexts.sectionSubtitle }}
      </p>
    </div>

    <div class="pricing-grid">
      @for (plan of pricingPlans; track plan.name) {
        <div class="pricing-card" [class.popular]="plan.isPopular">
          @if (plan.isPopular) {
            <div class="popular-badge">{{ pricingTexts.popular }}</div>
          }

          <div class="plan-header">
            <h3 class="plan-name">{{ plan.name }}</h3>
            <div class="plan-price">
              <span class="price">{{ plan.price }}</span>
              @if (plan.period) {
                <span class="period">{{ plan.period }}</span>
              }
            </div>
            <p class="plan-description">{{ plan.description }}</p>
          </div>

          <div class="plan-features">
            @for (feature of plan.features; track feature) {
              <div class="feature-item">
                <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <polyline points="20,6 9,17 4,12"/>
                </svg>
                <span>{{ feature }}</span>
              </div>
            }
          </div>

          <button
            class="plan-button btn"
            [class]="plan.buttonClass"
            (click)="onSelectPlan(plan)"
          >
            {{ plan.buttonText }}
          </button>
        </div>
      }
    </div>
  </div>
</section>

<!-- Contact Section -->
<section id="contact" class="contact">
  <div class="container">
    <div class="contact-content">
      <div class="contact-info">
        <h2 class="section-title">Get in Touch</h2>
        <p class="section-description">
          Ready to transform your business with AI? Contact our team to learn more about our services and find the perfect solution for your needs.
        </p>

        <div class="contact-methods">
          <div class="contact-method">
            <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
            <div>
              <h4>Email Us</h4>
              <p>contact&#64;edarah.com</p>
            </div>
          </div>

          <div class="contact-method">
            <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
            <div>
              <h4>Call Us</h4>
              <p>+20 102 969 7780</p>
            </div>
          </div>

          <div class="contact-method">
            <svg class="contact-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
              <circle cx="12" cy="10" r="3"/>
            </svg>
            <div>
              <h4>Visit Us</h4>
              <p>123 Elshahk Ali </p>
            </div>
          </div>
        </div>
      </div>

      <div class="contact-cta">
        <div class="cta-card">
          <h3>{{ contactTexts.sectionTitle }}</h3>
          <p>{{ contactTexts.sectionSubtitle }}</p>
          <button class="btn btn-primary btn-large" (click)="onGetStarted()">
            {{ heroTexts.startTrial }}
          </button>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-brand">
        <img src="/assets/images/edarah-logo.svg" alt="Edarah" class="footer-logo">
        <span class="brand-text">Edarah</span>
        <p class="footer-description">
          Empowering businesses with cutting-edge AI solutions for a smarter future.
        </p>
      </div>

      <div class="footer-links">
        <div class="link-group">
          <h4>Product</h4>
          <a href="#features">Features</a>
          <a href="#pricing">Pricing</a>
          <a href="#">API Documentation</a>
          <a href="#">SDKs</a>
        </div>

        <div class="link-group">
          <h4>Company</h4>
          <a href="#">About Us</a>
          <a href="#">Careers</a>
          <a href="#">Blog</a>
          <a href="#contact">Contact</a>
        </div>

        <div class="link-group">
          <h4>Support</h4>
          <a href="#">Help Center</a>
          <a href="#">Community</a>
          <a href="#">Status</a>
          <a href="#">Security</a>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; 2025 Edarah. All rights reserved.</p>
      <div class="footer-legal">
        <a href="#">Privacy Policy</a>
        <a href="#">Terms of Service</a>
        <a href="#">Cookie Policy</a>
      </div>
    </div>
  </div>
</footer>
