.navbar {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0.5rem 2rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  font-family: 'Inter', sans-serif;
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.navbar-logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #1a1a1a;
  font-size: 1.5rem;
  font-weight: 600;
}

.navbar-logo img {
  height: 40px;
  margin-right: 0.5rem;
}

.navbar-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #555;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #007bff;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-login {
  background-color: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-login:hover {
  background-color: #007bff;
  color: #fff;
}

.btn-register {
  background-color: #007bff;
  color: #fff;
  border: 1px solid #007bff;
}

.btn-register:hover {
  background-color: #0056b3;
}

.user-menu {
  position: relative;
}

.user-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.chevron-down {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.user-menu-dropdown {
  display: none;
  position: absolute;
  top: 120%;
  right: 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 150px;
  z-index: 1100;
}

.user-menu:hover .user-menu-dropdown {
  display: block;
}

.dropdown-item {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f0f0f0;
}

/* RTL Support */
:host-context([dir="rtl"]) .navbar-container {
  flex-direction: row-reverse;
}

:host-context([dir="rtl"]) .navbar-links {
  flex-direction: row-reverse;
}

:host-context([dir="rtl"]) .navbar-actions {
  flex-direction: row-reverse;
}

:host-context([dir="rtl"]) .navbar-logo img {
  margin-right: 0;
  margin-left: 0.5rem;
}

:host-context([dir="rtl"]) .user-menu-dropdown {
  right: auto;
  left: 0;
}

:host-context([dir="rtl"]) .user-menu-button {
  flex-direction: row-reverse;
}

/* Arabic font support */
:host-context(.arabic) {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}
