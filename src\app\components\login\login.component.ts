import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { LanguageService } from '../../services/language.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, RouterLink],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit, OnDestroy {
  loginForm: FormGroup;
  isSubmitting = false;
  showPassword = false;
  currentLanguage: string = 'en';
  private languageSubscription: Subscription = new Subscription();

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private authService: AuthService,
    private languageService: LanguageService
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }

  ngOnInit() {
    this.languageSubscription = this.languageService.currentLanguage$.subscribe(
      language => {
        this.currentLanguage = language;
      }
    );
  }

  ngOnDestroy() {
    this.languageSubscription.unsubscribe();
  }

  get email() {
    return this.loginForm.get('email');
  }

  get password() {
    return this.loginForm.get('password');
  }

  // Translation getters
  get pageTexts() {
    return {
      title: this.currentLanguage === 'en' ? 'Welcome Back' : 'مرحباً بعودتك',
      subtitle: this.currentLanguage === 'en' ? 'Sign in to access your AI services' : 'سجل الدخول للوصول إلى خدمات الذكاء الاصطناعي',
      emailLabel: this.currentLanguage === 'en' ? 'Email Address' : 'عنوان البريد الإلكتروني',
      emailPlaceholder: this.currentLanguage === 'en' ? 'Enter your email' : 'أدخل بريدك الإلكتروني',
      passwordLabel: this.currentLanguage === 'en' ? 'Password' : 'كلمة المرور',
      passwordPlaceholder: this.currentLanguage === 'en' ? 'Enter your password' : 'أدخل كلمة المرور',
      rememberMe: this.currentLanguage === 'en' ? 'Remember me' : 'تذكرني',
      forgotPassword: this.currentLanguage === 'en' ? 'Forgot password?' : 'نسيت كلمة المرور؟',
      signInButton: this.currentLanguage === 'en' ? 'Sign In' : 'تسجيل الدخول',
      signingIn: this.currentLanguage === 'en' ? 'Signing in...' : 'جاري تسجيل الدخول...',
      noAccount: this.currentLanguage === 'en' ? "Don't have an account?" : 'ليس لديك حساب؟',
      signUpLink: this.currentLanguage === 'en' ? 'Sign up here' : 'سجل هنا'
    };
  }

  get errorTexts() {
    return {
      emailRequired: this.currentLanguage === 'en' ? 'Email is required' : 'البريد الإلكتروني مطلوب',
      emailInvalid: this.currentLanguage === 'en' ? 'Please enter a valid email address' : 'يرجى إدخال عنوان بريد إلكتروني صحيح',
      passwordRequired: this.currentLanguage === 'en' ? 'Password is required' : 'كلمة المرور مطلوبة',
      passwordMinLength: this.currentLanguage === 'en' ? 'Password must be at least 6 characters' : 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
    };
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.isSubmitting = true;
      const { rememberMe, ...credentials } = this.loginForm.value;

      this.authService.login(credentials).subscribe({
        next: (response) => {
          console.log('Login successful', response);
          this.isSubmitting = false;
          this.router.navigate(['/home']);
        },
        error: (err) => {
          console.error('Login failed', err);
          this.isSubmitting = false;
          // You can display an error message to the user here
        }
      });
    } else {
      this.loginForm.markAllAsTouched();
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        if (fieldName === 'email') return this.errorTexts.emailRequired;
        if (fieldName === 'password') return this.errorTexts.passwordRequired;
      }
      if (field.errors['email']) {
        return this.errorTexts.emailInvalid;
      }
      if (field.errors['minlength']) {
        if (fieldName === 'password') return this.errorTexts.passwordMinLength;
      }
    }
    return '';
  }
}
