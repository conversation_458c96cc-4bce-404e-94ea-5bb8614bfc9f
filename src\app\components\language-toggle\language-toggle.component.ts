import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { LanguageService, Language } from '../../services/language.service';

@Component({
  selector: 'app-language-toggle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './language-toggle.component.html',
  styleUrl: './language-toggle.component.css'
})
export class LanguageToggleComponent implements OnInit, OnDestroy {
  currentLanguage: Language = 'en';
  private languageSubscription!: Subscription;

  constructor(private languageService: LanguageService) {}

  ngOnInit() {
    this.languageSubscription = this.languageService.currentLanguage$.subscribe(
      language => {
        this.currentLanguage = language;
      }
    );
  }

  ngOnDestroy() {
    if (this.languageSubscription) {
      this.languageSubscription.unsubscribe();
    }
  }

  toggleLanguage() {
    this.languageService.toggleLanguage();
  }

  get buttonText(): string {
    return this.currentLanguage === 'en' ? 'العربية' : 'English';
  }

  get buttonTitle(): string {
    return this.currentLanguage === 'en' ? 'Switch to Arabic' : 'Switch to English';
  }
}
