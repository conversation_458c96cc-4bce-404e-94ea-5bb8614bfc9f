import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export type Language = 'en' | 'ar';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private currentLanguageSubject = new BehaviorSubject<Language>('en');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  constructor() {
    // Load saved language preference or default to English
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
      this.setLanguage(savedLanguage);
    }
  }

  getCurrentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  setLanguage(language: Language) {
    this.currentLanguageSubject.next(language);
    localStorage.setItem('language', language);
    
    // Update document direction and language
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    
    // Add/remove Arabic class to body for styling
    if (language === 'ar') {
      document.body.classList.add('arabic');
    } else {
      document.body.classList.remove('arabic');
    }
  }

  toggleLanguage() {
    const currentLang = this.getCurrentLanguage();
    const newLang: Language = currentLang === 'en' ? 'ar' : 'en';
    this.setLanguage(newLang);
  }

  isArabic(): boolean {
    return this.getCurrentLanguage() === 'ar';
  }

  isEnglish(): boolean {
    return this.getCurrentLanguage() === 'en';
  }
}
